# 公安内部单位全景图可视化系统

一个基于Vue3 + Vite + TypeScript开发的公安内部单位全景图可视化系统，以保定市第一中心医院为示例数据。

## 功能特性

### 核心功能
- **中心节点展示**：以选定单位为中心，展示基本信息（名称、地址、所属派出所、重点等级等）
- **信息模块辐射**：围绕中心节点辐射状分布各个信息模块
- **实时数据展示**：信息模块直接展示具体内容，无需点击查看
- **拖拽交互**：支持所有卡片的自由拖拽和位置调整
- **动态连线**：中心节点与各信息模块间的动态连接线效果

### 信息模块
1. **三防信息**：监控数量、配备保安人数、设备状态
2. **专职保卫**：负责人信息、联系方式
3. **案件信息**：今年/去年案件数据对比、案件类型统计
4. **行业信息**：根据单位类型显示关键数据（医院：医护人员、门诊量等）
5. **背审情况**：背景审查完成情况、风险评估
6. **工作绩效**：责任民警信息、检查完成情况
7. **风险评估**：评估分数、风险等级、问题和建议

### 视觉效果
- **科技感设计**：深色背景配合科技蓝色调
- **发光连线**：鼠标悬停时连线发光效果
- **动态背景**：粒子动画、网格背景、光晕效果
- **半透明卡片**：毛玻璃效果和边框发光
- **响应式设计**：支持不同屏幕尺寸

### 交互功能
- **拖拽支持**：所有卡片支持自由拖拽
- **悬停效果**：鼠标悬停时的视觉反馈
- **布局管理**：重置布局、保存/加载布局
- **缩放适配**：自动适应不同屏幕尺寸

## 技术栈

- **前端框架**：Vue 3 (Composition API)
- **构建工具**：Vite
- **语言**：TypeScript
- **状态管理**：Pinia
- **路由**：Vue Router
- **代码规范**：ESLint

## 快速开始

### 环境要求
- Node.js >= 16
- npm >= 7

### 安装依赖
```sh
npm install
```

### 开发模式
```sh
npm run dev
```

### 构建生产版本
```sh
npm run build
```

### 代码检查
```sh
npm run lint
```

## 使用说明

1. **查看信息**：页面加载后自动显示保定市第一中心医院的全景信息
2. **拖拽卡片**：点击并拖拽任意卡片到新位置
3. **查看连线**：鼠标悬停在卡片上查看连接线效果
4. **重置布局**：点击右上角"重置布局"按钮恢复默认位置
5. **保存布局**：点击"保存布局"按钮将当前布局保存到本地存储

## 数据定制

要使用其他单位的数据，请修改 `src/data/sampleData.ts` 文件中的数据结构。数据格式参考 `src/types/index.ts` 中的类型定义。

## 浏览器支持

- Chrome >= 88
- Firefox >= 85
- Safari >= 14
- Edge >= 88

## 项目结构

```
src/
├── components/          # 组件目录
│   ├── CenterCard.vue          # 中心单位卡片
│   ├── InfoCard.vue            # 通用信息卡片
│   ├── SecurityInfoCard.vue    # 三防信息卡片
│   ├── GuardInfoCard.vue       # 专职保卫卡片
│   ├── CaseInfoCard.vue        # 案件信息卡片
│   ├── IndustryInfoCard.vue    # 行业信息卡片
│   ├── BackgroundCheckCard.vue # 背审情况卡片
│   ├── PerformanceCard.vue     # 工作绩效卡片
│   ├── RiskAssessmentCard.vue  # 风险评估卡片
│   ├── ConnectionLines.vue     # 连接线组件
│   └── ParticleBackground.vue  # 粒子背景组件
├── stores/              # 状态管理
│   └── panorama.ts             # 全景图状态管理
├── types/               # 类型定义
│   └── index.ts                # 数据类型定义
├── data/                # 示例数据
│   └── sampleData.ts           # 保定市第一中心医院数据
└── assets/              # 静态资源
    └── main.css                # 全局样式
```

## 开发团队

基于Vue3 + Vite技术栈开发，采用现代化的前端开发模式。
