import type { UnitData } from '@/types'

export const baodingHospitalData: UnitData = {
  unitInfo: {
    name: '保定市第一中心医院',
    district: '保定市莲池区',
    address: '莲池区长城北大街320号',
    policeStation: '莲池分局五四路派出所',
    priorityLevel: 'high',
    priorityText: '一级重点'
  },
  securityInfo: {
    monitorCount: 268,
    securityPersonnel: 32,
    equipmentStatus: '全部正常运行'
  },
  guardInfo: {
    name: '李建军',
    phone: '138****6688',
    backupContact: '王志强',
    backupPhone: '139****7799'
  },
  caseInfo: {
    thisYear: {
      criminal: 8,
      administrative: 15,
      theft: 5,
      robbery: 1
    },
    lastYear: {
      criminal: 12,
      administrative: 18,
      theft: 8,
      robbery: 2
    }
  },
  industryInfo: {
    type: 'hospital',
    medicalStaff: 1847,
    dailyPatients: 3200,
    employees: 2156
  },
  backgroundCheckInfo: {
    completed: 156,
    noRisk: 148,
    pending: 8
  },
  performanceInfo: {
    officer: '张国强',
    badgeNumber: '110523',
    checkFrequency: '每月3次例行检查',
    monthlyCompleted: 3,
    issuesFound: 5,
    issuesResolved: 4
  },
  riskAssessmentInfo: {
    score: 78,
    level: 'medium',
    levelText: '中等风险',
    mainIssues: [
      '夜间安保力量相对不足',
      '部分区域监控盲区需要补充',
      '应急预案演练频次偏低'
    ],
    suggestions: [
      '增加夜班保安4人',
      '补充安装监控设备12个',
      '每季度组织应急演练'
    ]
  }
}
