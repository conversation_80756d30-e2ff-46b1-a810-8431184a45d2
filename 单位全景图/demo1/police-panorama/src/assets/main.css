@import './base.css';

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
  overflow: hidden;
  background: linear-gradient(135deg, #0a192f 0%, #0c213a 100%);
  font-family: 'Microsoft YaHei', Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  color: #e6f1ff;
}

#app {
  width: 100vw;
  height: 100vh;
  margin: 0;
  padding: 0;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(13, 71, 161, 0.1);
}

::-webkit-scrollbar-thumb {
  background: rgba(33, 150, 243, 0.3);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(33, 150, 243, 0.5);
}

/* 选择文本样式 */
::selection {
  background: rgba(100, 181, 246, 0.3);
  color: #e6f1ff;
}
