// 基础单位信息
export interface UnitInfo {
  name: string
  district: string
  address: string
  policeStation: string
  priorityLevel: 'high' | 'medium' | 'low'
  priorityText: string
}

// 三防信息
export interface SecurityInfo {
  monitorCount: number
  securityPersonnel: number
  equipmentStatus: string
}

// 专职保卫信息
export interface GuardInfo {
  name: string
  phone: string
  backupContact: string
  backupPhone: string
}

// 案件信息
export interface CaseInfo {
  thisYear: {
    criminal: number
    administrative: number
    theft: number
    robbery: number
  }
  lastYear: {
    criminal: number
    administrative: number
    theft: number
    robbery: number
  }
}

// 行业信息（医院）
export interface IndustryInfo {
  type: 'hospital' | 'school' | 'bank' | 'other'
  students?: number
  teachers?: number
  medicalStaff?: number
  dailyPatients?: number
  employees?: number
}

// 背审情况
export interface BackgroundCheckInfo {
  completed: number
  noRisk: number
  pending: number
}

// 工作绩效
export interface PerformanceInfo {
  officer: string
  badgeNumber: string
  checkFrequency: string
  monthlyCompleted: number
  issuesFound: number
  issuesResolved: number
}

// 风险评估
export interface RiskAssessmentInfo {
  score: number
  level: 'high' | 'medium' | 'low'
  levelText: string
  mainIssues: string[]
  suggestions: string[]
}

// 完整的单位数据
export interface UnitData {
  unitInfo: UnitInfo
  securityInfo: SecurityInfo
  guardInfo: GuardInfo
  caseInfo: CaseInfo
  industryInfo: IndustryInfo
  backgroundCheckInfo: BackgroundCheckInfo
  performanceInfo: PerformanceInfo
  riskAssessmentInfo: RiskAssessmentInfo
}

// 卡片位置信息
export interface CardPosition {
  x: number
  y: number
  id: string
}

// 连线信息
export interface ConnectionLine {
  from: string
  to: string
  active: boolean
}
