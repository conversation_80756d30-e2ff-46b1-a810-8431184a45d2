import { defineStore } from 'pinia'
import { ref, reactive } from 'vue'
import type { UnitData, CardPosition, ConnectionLine } from '@/types'
import { baodingHospitalData } from '@/data/sampleData'

export const usePanoramaStore = defineStore('panorama', () => {
  // 单位数据
  const unitData = ref<UnitData>(baodingHospitalData)
  
  // 卡片位置信息
  const cardPositions = reactive<Record<string, CardPosition>>({
    center: { id: 'center', x: 50, y: 50 },
    security: { id: 'security', x: 20, y: 20 },
    guard: { id: 'guard', x: 80, y: 20 },
    case: { id: 'case', x: 20, y: 80 },
    industry: { id: 'industry', x: 80, y: 80 },
    background: { id: 'background', x: 5, y: 50 },
    performance: { id: 'performance', x: 95, y: 50 },
    risk: { id: 'risk', x: 50, y: 10 }
  })
  
  // 连线状态
  const connectionLines = ref<ConnectionLine[]>([
    { from: 'center', to: 'security', active: false },
    { from: 'center', to: 'guard', active: false },
    { from: 'center', to: 'case', active: false },
    { from: 'center', to: 'industry', active: false },
    { from: 'center', to: 'background', active: false },
    { from: 'center', to: 'performance', active: false },
    { from: 'center', to: 'risk', active: false }
  ])
  
  // 拖拽状态
  const isDragging = ref(false)
  const draggedCard = ref<string | null>(null)
  
  // 更新卡片位置
  const updateCardPosition = (cardId: string, x: number, y: number) => {
    if (cardPositions[cardId]) {
      cardPositions[cardId].x = x
      cardPositions[cardId].y = y
    }
  }
  
  // 设置连线激活状态
  const setConnectionActive = (from: string, to: string, active: boolean) => {
    const line = connectionLines.value.find(l => l.from === from && l.to === to)
    if (line) {
      line.active = active
    }
  }
  
  // 开始拖拽
  const startDrag = (cardId: string) => {
    isDragging.value = true
    draggedCard.value = cardId
  }
  
  // 结束拖拽
  const endDrag = () => {
    isDragging.value = false
    draggedCard.value = null
  }
  
  // 重置布局
  const resetLayout = () => {
    cardPositions.center = { id: 'center', x: 50, y: 50 }
    cardPositions.security = { id: 'security', x: 20, y: 20 }
    cardPositions.guard = { id: 'guard', x: 80, y: 20 }
    cardPositions.case = { id: 'case', x: 20, y: 80 }
    cardPositions.industry = { id: 'industry', x: 80, y: 80 }
    cardPositions.background = { id: 'background', x: 5, y: 50 }
    cardPositions.performance = { id: 'performance', x: 95, y: 50 }
    cardPositions.risk = { id: 'risk', x: 50, y: 10 }
  }

  // 保存布局
  const saveLayout = () => {
    const layout = JSON.stringify(cardPositions)
    localStorage.setItem('panorama-layout', layout)
  }

  // 加载布局
  const loadLayout = () => {
    const savedLayout = localStorage.getItem('panorama-layout')
    if (savedLayout) {
      try {
        const layout = JSON.parse(savedLayout)
        Object.assign(cardPositions, layout)
      } catch (error) {
        console.warn('Failed to load saved layout:', error)
      }
    }
  }
  
  return {
    unitData,
    cardPositions,
    connectionLines,
    isDragging,
    draggedCard,
    updateCardPosition,
    setConnectionActive,
    startDrag,
    endDrag,
    resetLayout,
    saveLayout,
    loadLayout
  }
})
