<template>
  <InfoCard card-id="guard" title="专职保卫">
    <div class="card-item">
      <div class="item-label">负责人姓名：</div>
      <div class="item-value">{{ guardInfo.name }}</div>
    </div>
    <div class="card-item">
      <div class="item-label">联系方式：</div>
      <div class="item-value">{{ guardInfo.phone }}</div>
    </div>
    <div class="card-item">
      <div class="item-label">备用联系人：</div>
      <div class="item-value">{{ guardInfo.backupContact }} {{ guardInfo.backupPhone }}</div>
    </div>
  </InfoCard>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import InfoCard from './InfoCard.vue'
import { usePanoramaStore } from '@/stores/panorama'

const store = usePanoramaStore()
const guardInfo = computed(() => store.unitData.guardInfo)
</script>

<style scoped>
.card-item {
  margin-bottom: 12px;
  padding-bottom: 12px;
  border-bottom: 1px solid rgba(144, 202, 249, 0.2);
}

.card-item:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.item-label {
  color: #90caf9;
  font-weight: 500;
  margin-bottom: 4px;
}

.item-value {
  color: #e3f2fd;
  font-weight: 300;
}
</style>
