<template>
  <InfoCard card-id="security" title="三防信息">
    <div class="card-item">
      <div class="item-label">监控数量：</div>
      <div class="item-value highlight">{{ securityInfo.monitorCount }}个</div>
    </div>
    <div class="card-item">
      <div class="item-label">配备保安人数：</div>
      <div class="item-value highlight">{{ securityInfo.securityPersonnel }}人</div>
    </div>
    <div class="card-item">
      <div class="item-label">安防设备状态：</div>
      <div class="item-value success">{{ securityInfo.equipmentStatus }}</div>
    </div>
  </InfoCard>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import InfoCard from './InfoCard.vue'
import { usePanoramaStore } from '@/stores/panorama'

const store = usePanoramaStore()
const securityInfo = computed(() => store.unitData.securityInfo)
</script>

<style scoped>
.card-item {
  margin-bottom: 12px;
  padding-bottom: 12px;
  border-bottom: 1px solid rgba(144, 202, 249, 0.2);
}

.card-item:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.item-label {
  color: #90caf9;
  font-weight: 500;
  margin-bottom: 4px;
}

.item-value {
  color: #e3f2fd;
  font-weight: 300;
}

.highlight {
  color: #4fc3f7;
  font-weight: bold;
}

.success {
  color: #81c784;
  font-weight: bold;
}
</style>
