<template>
  <InfoCard card-id="industry" title="行业信息">
    <div class="card-item" v-if="industryInfo.type === 'hospital'">
      <div class="item-label">医护人员：</div>
      <div class="item-value highlight">{{ industryInfo.medicalStaff }}人</div>
    </div>
    <div class="card-item" v-if="industryInfo.type === 'hospital'">
      <div class="item-label">日均门诊量：</div>
      <div class="item-value">{{ industryInfo.dailyPatients }}人次</div>
    </div>
    <div class="card-item">
      <div class="item-label">总员工数：</div>
      <div class="item-value">{{ industryInfo.employees }}人</div>
    </div>
    <div class="card-item" v-if="industryInfo.type === 'school'">
      <div class="item-label">学生人数：</div>
      <div class="item-value highlight">{{ industryInfo.students }}人</div>
    </div>
    <div class="card-item" v-if="industryInfo.type === 'school'">
      <div class="item-label">教职工人数：</div>
      <div class="item-value">{{ industryInfo.teachers }}人</div>
    </div>
  </InfoCard>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import InfoCard from './InfoCard.vue'
import { usePanoramaStore } from '@/stores/panorama'

const store = usePanoramaStore()
const industryInfo = computed(() => store.unitData.industryInfo)
</script>

<style scoped>
.card-item {
  margin-bottom: 12px;
  padding-bottom: 12px;
  border-bottom: 1px solid rgba(144, 202, 249, 0.2);
}

.card-item:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.item-label {
  color: #90caf9;
  font-weight: 500;
  margin-bottom: 4px;
}

.item-value {
  color: #e3f2fd;
  font-weight: 300;
}

.highlight {
  color: #4fc3f7;
  font-weight: bold;
}
</style>
