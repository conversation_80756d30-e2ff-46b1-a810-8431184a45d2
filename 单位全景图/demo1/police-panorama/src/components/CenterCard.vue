<template>
  <div 
    class="center-card"
    :style="cardStyle"
    @mousedown="handleMouseDown"
    @mouseenter="handleMouseEnter"
    @mouseleave="handleMouseLeave"
  >
    <div class="unit-name">{{ unitData.unitInfo.name }}</div>
    <div class="unit-info">
      <div class="info-item">
        <div class="info-label">所属区县：</div>
        <div class="info-value">{{ unitData.unitInfo.district }}</div>
      </div>
      <div class="info-item">
        <div class="info-label">地址：</div>
        <div class="info-value">{{ unitData.unitInfo.address }}</div>
      </div>
      <div class="info-item">
        <div class="info-label">所属派出所：</div>
        <div class="info-value">{{ unitData.unitInfo.policeStation }}</div>
      </div>
      <div class="info-item">
        <div class="info-label">重点等级：</div>
        <div class="info-value">
          {{ unitData.unitInfo.priorityText }}
          <span :class="['priority-level', unitData.unitInfo.priorityLevel]">
            {{ getPriorityText(unitData.unitInfo.priorityLevel) }}
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { usePanoramaStore } from '@/stores/panorama'

const store = usePanoramaStore()
const { unitData, cardPositions } = store

const cardStyle = computed(() => ({
  left: `${cardPositions.center.x}%`,
  top: `${cardPositions.center.y}%`,
  transform: 'translate(-50%, -50%)'
}))

const getPriorityText = (level: string) => {
  const texts = {
    high: '高危',
    medium: '中等',
    low: '低风险'
  }
  return texts[level as keyof typeof texts] || '未知'
}

const handleMouseDown = (e: MouseEvent) => {
  store.startDrag('center')
  // 这里可以添加拖拽逻辑
}

const handleMouseEnter = () => {
  // 激活所有连线
  store.connectionLines.forEach(line => {
    if (line.from === 'center') {
      store.setConnectionActive(line.from, line.to, true)
    }
  })
}

const handleMouseLeave = () => {
  // 取消激活连线
  store.connectionLines.forEach(line => {
    if (line.from === 'center') {
      store.setConnectionActive(line.from, line.to, false)
    }
  })
}
</script>

<style scoped>
.center-card {
  position: absolute;
  width: 320px;
  height: 280px;
  background: linear-gradient(145deg, rgba(25, 118, 210, 0.2), rgba(33, 150, 243, 0.1));
  border: 1px solid rgba(33, 150, 243, 0.3);
  border-radius: 15px;
  padding: 25px;
  backdrop-filter: blur(10px);
  box-shadow: 
    0 0 30px rgba(33, 150, 243, 0.2),
    inset 0 0 20px rgba(255, 255, 255, 0.05);
  z-index: 10;
  cursor: move;
  transition: all 0.3s ease;
  color: #e6f1ff;
  font-family: 'Microsoft YaHei', Arial, sans-serif;
}

.center-card:hover {
  transform: translate(-50%, -50%) scale(1.02) !important;
  box-shadow: 
    0 0 50px rgba(33, 150, 243, 0.4),
    inset 0 0 30px rgba(255, 255, 255, 0.1);
}

.unit-name {
  font-size: 24px;
  font-weight: bold;
  color: #64b5f6;
  text-align: center;
  margin-bottom: 20px;
  text-shadow: 0 0 10px rgba(100, 181, 246, 0.5);
}

.unit-info {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
}

.info-item {
  font-size: 14px;
  line-height: 1.6;
}

.info-label {
  color: #90caf9;
  font-weight: 500;
}

.info-value {
  color: #e3f2fd;
  font-weight: 300;
}

.priority-level {
  display: inline-block;
  padding: 3px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: bold;
  margin-left: 8px;
  color: white;
}

.priority-level.high {
  background: linear-gradient(45deg, #f44336, #ff5722);
}

.priority-level.medium {
  background: linear-gradient(45deg, #ff9800, #ffc107);
}

.priority-level.low {
  background: linear-gradient(45deg, #4caf50, #8bc34a);
}
</style>
