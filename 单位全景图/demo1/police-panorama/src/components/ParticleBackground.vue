<template>
  <div class="particle-background">
    <div 
      v-for="particle in particles" 
      :key="particle.id"
      class="particle"
      :style="getParticleStyle(particle)"
    ></div>
    
    <!-- 网格背景 -->
    <div class="grid-background">
      <div 
        v-for="i in gridLines.vertical" 
        :key="`v-${i}`"
        class="grid-line vertical"
        :style="{ left: `${(i / gridLines.vertical) * 100}%` }"
      ></div>
      <div 
        v-for="i in gridLines.horizontal" 
        :key="`h-${i}`"
        class="grid-line horizontal"
        :style="{ top: `${(i / gridLines.horizontal) * 100}%` }"
      ></div>
    </div>
    
    <!-- 光晕效果 -->
    <div class="glow-effects">
      <div class="glow glow-1"></div>
      <div class="glow glow-2"></div>
      <div class="glow glow-3"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'

interface Particle {
  id: number
  x: number
  y: number
  size: number
  speed: number
  opacity: number
  color: string
}

const particles = ref<Particle[]>([])
const gridLines = ref({
  vertical: 20,
  horizontal: 15
})

let animationFrame: number

const createParticles = () => {
  particles.value = []
  for (let i = 0; i < 50; i++) {
    particles.value.push({
      id: i,
      x: Math.random() * 100,
      y: Math.random() * 100,
      size: Math.random() * 3 + 1,
      speed: Math.random() * 0.5 + 0.1,
      opacity: Math.random() * 0.6 + 0.2,
      color: `rgba(100, 181, 246, ${Math.random() * 0.5 + 0.3})`
    })
  }
}

const updateParticles = () => {
  particles.value.forEach(particle => {
    particle.y -= particle.speed
    particle.opacity -= 0.001
    
    if (particle.y < -5 || particle.opacity <= 0) {
      particle.y = 105
      particle.x = Math.random() * 100
      particle.opacity = Math.random() * 0.6 + 0.2
    }
  })
}

const getParticleStyle = (particle: Particle) => ({
  left: `${particle.x}%`,
  top: `${particle.y}%`,
  width: `${particle.size}px`,
  height: `${particle.size}px`,
  backgroundColor: particle.color,
  opacity: particle.opacity
})

const animate = () => {
  updateParticles()
  animationFrame = requestAnimationFrame(animate)
}

onMounted(() => {
  createParticles()
  animate()
})

onUnmounted(() => {
  if (animationFrame) {
    cancelAnimationFrame(animationFrame)
  }
})
</script>

<style scoped>
.particle-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0;
  overflow: hidden;
}

.particle {
  position: absolute;
  border-radius: 50%;
  animation: twinkle 3s ease-in-out infinite;
}

.grid-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0.1;
}

.grid-line {
  position: absolute;
  background: linear-gradient(90deg, transparent, rgba(33, 150, 243, 0.3), transparent);
}

.grid-line.vertical {
  width: 1px;
  height: 100%;
  background: linear-gradient(180deg, transparent, rgba(33, 150, 243, 0.2), transparent);
}

.grid-line.horizontal {
  width: 100%;
  height: 1px;
}

.glow-effects {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.glow {
  position: absolute;
  border-radius: 50%;
  filter: blur(40px);
  animation: float 6s ease-in-out infinite;
}

.glow-1 {
  width: 300px;
  height: 300px;
  background: radial-gradient(circle, rgba(33, 150, 243, 0.1) 0%, transparent 70%);
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.glow-2 {
  width: 400px;
  height: 400px;
  background: radial-gradient(circle, rgba(21, 101, 192, 0.08) 0%, transparent 70%);
  top: 60%;
  right: 10%;
  animation-delay: 2s;
}

.glow-3 {
  width: 250px;
  height: 250px;
  background: radial-gradient(circle, rgba(100, 181, 246, 0.12) 0%, transparent 70%);
  bottom: 20%;
  left: 50%;
  animation-delay: 4s;
}

@keyframes twinkle {
  0%, 100% { opacity: 0.3; transform: scale(1); }
  50% { opacity: 1; transform: scale(1.2); }
}

@keyframes float {
  0%, 100% { transform: translateY(0px) translateX(0px); }
  33% { transform: translateY(-20px) translateX(10px); }
  66% { transform: translateY(10px) translateX(-15px); }
}
</style>
