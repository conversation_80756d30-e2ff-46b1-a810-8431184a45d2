<template>
  <InfoCard card-id="background" title="背审情况">
    <div class="card-item">
      <div class="item-label">已完成背审：</div>
      <div class="item-value highlight">{{ backgroundInfo.completed }}人</div>
    </div>
    <div class="card-item">
      <div class="item-label">无风险人员：</div>
      <div class="item-value success">{{ backgroundInfo.noRisk }}人</div>
    </div>
    <div class="card-item">
      <div class="item-label">待审核：</div>
      <div class="item-value">{{ backgroundInfo.pending }}人</div>
    </div>
    <div class="card-item">
      <div class="item-label">完成率：</div>
      <div class="item-value highlight">{{ completionRate }}%</div>
    </div>
  </InfoCard>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import InfoCard from './InfoCard.vue'
import { usePanoramaStore } from '@/stores/panorama'

const store = usePanoramaStore()
const backgroundInfo = computed(() => store.unitData.backgroundCheckInfo)

const completionRate = computed(() => {
  const total = backgroundInfo.value.completed + backgroundInfo.value.pending
  return total > 0 ? Math.round((backgroundInfo.value.completed / total) * 100) : 0
})
</script>

<style scoped>
.card-item {
  margin-bottom: 12px;
  padding-bottom: 12px;
  border-bottom: 1px solid rgba(144, 202, 249, 0.2);
}

.card-item:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.item-label {
  color: #90caf9;
  font-weight: 500;
  margin-bottom: 4px;
}

.item-value {
  color: #e3f2fd;
  font-weight: 300;
}

.highlight {
  color: #4fc3f7;
  font-weight: bold;
}

.success {
  color: #81c784;
  font-weight: bold;
}
</style>
