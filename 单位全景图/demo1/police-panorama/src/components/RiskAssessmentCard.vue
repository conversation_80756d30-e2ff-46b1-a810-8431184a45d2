<template>
  <InfoCard card-id="risk" title="风险评估">
    <div class="card-item">
      <div class="item-label">评估分数：</div>
      <div class="item-value highlight">{{ riskInfo.score }}分</div>
    </div>
    <div class="card-item">
      <div class="item-label">风险等级：</div>
      <div :class="['item-value', getRiskLevelClass()]">{{ riskInfo.levelText }}</div>
    </div>
    <div class="card-item">
      <div class="item-label">主要问题：</div>
      <div class="item-value">
        <div v-for="(issue, index) in riskInfo.mainIssues" :key="index" class="issue-item">
          {{ index + 1 }}. {{ issue }}
        </div>
      </div>
    </div>
    <div class="card-item">
      <div class="item-label">整改建议：</div>
      <div class="item-value">
        <div v-for="(suggestion, index) in riskInfo.suggestions" :key="index" class="suggestion-item">
          {{ index + 1 }}. {{ suggestion }}
        </div>
      </div>
    </div>
  </InfoCard>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import InfoCard from './InfoCard.vue'
import { usePanoramaStore } from '@/stores/panorama'

const store = usePanoramaStore()
const riskInfo = computed(() => store.unitData.riskAssessmentInfo)

const getRiskLevelClass = () => {
  switch (riskInfo.value.level) {
    case 'high':
      return 'warning'
    case 'medium':
      return 'medium-risk'
    case 'low':
      return 'success'
    default:
      return ''
  }
}
</script>

<style scoped>
.card-item {
  margin-bottom: 12px;
  padding-bottom: 12px;
  border-bottom: 1px solid rgba(144, 202, 249, 0.2);
}

.card-item:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.item-label {
  color: #90caf9;
  font-weight: 500;
  margin-bottom: 4px;
}

.item-value {
  color: #e3f2fd;
  font-weight: 300;
}

.highlight {
  color: #4fc3f7;
  font-weight: bold;
}

.warning {
  color: #ff8a65;
  font-weight: bold;
}

.medium-risk {
  color: #ffb74d;
  font-weight: bold;
}

.success {
  color: #81c784;
  font-weight: bold;
}

.issue-item,
.suggestion-item {
  margin-bottom: 4px;
  font-size: 13px;
  line-height: 1.4;
}
</style>
