<template>
  <InfoCard card-id="performance" title="工作绩效">
    <div class="card-item">
      <div class="item-label">责任民警：</div>
      <div class="item-value">{{ performanceInfo.officer }}（警号：{{ performanceInfo.badgeNumber }}）</div>
    </div>
    <div class="card-item">
      <div class="item-label">检查频次：</div>
      <div class="item-value highlight">{{ performanceInfo.checkFrequency }}</div>
    </div>
    <div class="card-item">
      <div class="item-label">本月完成：</div>
      <div class="item-value">{{ performanceInfo.monthlyCompleted }}次安全检查</div>
    </div>
    <div class="card-item">
      <div class="item-label">发现问题：</div>
      <div class="item-value">{{ performanceInfo.issuesFound }}项</div>
    </div>
    <div class="card-item">
      <div class="item-label">已整改：</div>
      <div class="item-value success">{{ performanceInfo.issuesResolved }}项</div>
    </div>
  </InfoCard>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import InfoCard from './InfoCard.vue'
import { usePanoramaStore } from '@/stores/panorama'

const store = usePanoramaStore()
const performanceInfo = computed(() => store.unitData.performanceInfo)
</script>

<style scoped>
.card-item {
  margin-bottom: 12px;
  padding-bottom: 12px;
  border-bottom: 1px solid rgba(144, 202, 249, 0.2);
}

.card-item:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.item-label {
  color: #90caf9;
  font-weight: 500;
  margin-bottom: 4px;
}

.item-value {
  color: #e3f2fd;
  font-weight: 300;
}

.highlight {
  color: #4fc3f7;
  font-weight: bold;
}

.success {
  color: #81c784;
  font-weight: bold;
}
</style>
