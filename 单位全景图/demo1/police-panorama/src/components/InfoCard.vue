<template>
  <div 
    :class="['info-card', { dragging: isDragging && draggedCard === cardId }]"
    :style="cardStyle"
    @mousedown="handleMouseDown"
    @mouseenter="handleMouseEnter"
    @mouseleave="handleMouseLeave"
  >
    <div class="card-title">{{ title }}</div>
    <div class="card-content">
      <slot></slot>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { usePanoramaStore } from '@/stores/panorama'

interface Props {
  cardId: string
  title: string
}

const props = defineProps<Props>()
const store = usePanoramaStore()
const { cardPositions, isDragging, draggedCard } = store

const cardStyle = computed(() => ({
  left: `${cardPositions[props.cardId].x}%`,
  top: `${cardPositions[props.cardId].y}%`
}))

const handleMouseDown = (e: MouseEvent) => {
  store.startDrag(props.cardId)
  // 这里可以添加拖拽逻辑
}

const handleMouseEnter = () => {
  // 激活连线
  store.setConnectionActive('center', props.cardId, true)
}

const handleMouseLeave = () => {
  // 取消激活连线
  store.setConnectionActive('center', props.cardId, false)
}
</script>

<style scoped>
.info-card {
  position: absolute;
  width: 280px;
  min-height: 200px;
  background: linear-gradient(145deg, rgba(13, 71, 161, 0.3), rgba(21, 101, 192, 0.2));
  border: 1px solid rgba(33, 150, 243, 0.2);
  border-radius: 12px;
  padding: 20px;
  backdrop-filter: blur(8px);
  box-shadow: 
    0 0 20px rgba(33, 150, 243, 0.15),
    inset 0 0 15px rgba(255, 255, 255, 0.03);
  cursor: move;
  transition: all 0.3s ease;
  user-select: none;
  color: #e6f1ff;
  font-family: 'Microsoft YaHei', Arial, sans-serif;
  transform-origin: center;
}

.info-card:hover {
  transform: scale(1.02);
  box-shadow: 
    0 0 40px rgba(33, 150, 243, 0.5),
    inset 0 0 25px rgba(255, 255, 255, 0.1);
}

.info-card.dragging {
  opacity: 0.8;
  transform: scale(1.02);
  box-shadow: 
    0 0 40px rgba(33, 150, 243, 0.5),
    inset 0 0 25px rgba(255, 255, 255, 0.1);
  z-index: 1000 !important;
}

.card-title {
  font-size: 18px;
  font-weight: bold;
  color: #4fc3f7;
  margin-bottom: 15px;
  text-align: center;
  position: relative;
  cursor: move;
}

.card-title::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 50%;
  transform: translateX(-50%);
  width: 40px;
  height: 2px;
  background: linear-gradient(90deg, transparent, #4fc3f7, transparent);
}

.card-content {
  font-size: 14px;
  line-height: 1.8;
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .info-card {
    width: 240px;
    min-height: 180px;
    padding: 15px;
  }
}
</style>
