<template>
  <svg class="connection-lines" :width="containerWidth" :height="containerHeight">
    <defs>
      <linearGradient id="lineGradient" x1="0%" y1="0%" x2="100%" y2="0%">
        <stop offset="0%" style="stop-color:transparent;stop-opacity:0" />
        <stop offset="50%" style="stop-color:#2196f3;stop-opacity:0.6" />
        <stop offset="100%" style="stop-color:transparent;stop-opacity:0" />
      </linearGradient>
      <linearGradient id="activeLineGradient" x1="0%" y1="0%" x2="100%" y2="0%">
        <stop offset="0%" style="stop-color:transparent;stop-opacity:0" />
        <stop offset="50%" style="stop-color:#64b5f6;stop-opacity:1" />
        <stop offset="100%" style="stop-color:transparent;stop-opacity:0" />
      </linearGradient>
      <filter id="glow">
        <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
        <feMerge> 
          <feMergeNode in="coloredBlur"/>
          <feMergeNode in="SourceGraphic"/>
        </feMerge>
      </filter>
    </defs>
    
    <line
      v-for="line in connectionLines"
      :key="`${line.from}-${line.to}`"
      :x1="getCardCenter(line.from).x"
      :y1="getCardCenter(line.from).y"
      :x2="getCardCenter(line.to).x"
      :y2="getCardCenter(line.to).y"
      :stroke="line.active ? 'url(#activeLineGradient)' : 'url(#lineGradient)'"
      :stroke-width="line.active ? '2' : '1'"
      :filter="line.active ? 'url(#glow)' : 'none'"
      class="connection-line"
      :class="{ active: line.active }"
    />
    
    <!-- 动态粒子效果 -->
    <circle
      v-for="(particle, index) in particles"
      :key="index"
      :cx="particle.x"
      :cy="particle.y"
      :r="particle.size"
      :fill="particle.color"
      :opacity="particle.opacity"
      class="particle"
    />
  </svg>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { usePanoramaStore } from '@/stores/panorama'

const store = usePanoramaStore()
const { cardPositions, connectionLines } = store

const containerWidth = ref(window.innerWidth)
const containerHeight = ref(window.innerHeight)

// 粒子系统
const particles = ref<Array<{
  x: number
  y: number
  size: number
  color: string
  opacity: number
}>>([])

const getCardCenter = (cardId: string) => {
  const position = cardPositions[cardId]
  if (!position) return { x: 0, y: 0 }
  
  return {
    x: (position.x / 100) * containerWidth.value,
    y: (position.y / 100) * containerHeight.value
  }
}

// 创建粒子
const createParticles = () => {
  particles.value = []
  for (let i = 0; i < 30; i++) {
    particles.value.push({
      x: Math.random() * containerWidth.value,
      y: Math.random() * containerHeight.value,
      size: Math.random() * 2 + 1,
      color: `rgba(100, 181, 246, ${Math.random() * 0.5 + 0.2})`,
      opacity: Math.random() * 0.5 + 0.2
    })
  }
}

// 更新粒子位置
const updateParticles = () => {
  particles.value.forEach(particle => {
    particle.y -= 0.5
    particle.opacity -= 0.002
    
    if (particle.y < 0 || particle.opacity <= 0) {
      particle.y = containerHeight.value
      particle.x = Math.random() * containerWidth.value
      particle.opacity = Math.random() * 0.5 + 0.2
    }
  })
}

let animationFrame: number

const animate = () => {
  updateParticles()
  animationFrame = requestAnimationFrame(animate)
}

const handleResize = () => {
  containerWidth.value = window.innerWidth
  containerHeight.value = window.innerHeight
}

onMounted(() => {
  createParticles()
  animate()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  if (animationFrame) {
    cancelAnimationFrame(animationFrame)
  }
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
.connection-lines {
  position: fixed;
  top: 0;
  left: 0;
  pointer-events: none;
  z-index: 1;
}

.connection-line {
  transition: all 0.3s ease;
}

.connection-line.active {
  animation: pulse 2s ease-in-out infinite;
}

.particle {
  animation: float 3s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 0.6; }
  50% { opacity: 1; }
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-5px); }
}
</style>
