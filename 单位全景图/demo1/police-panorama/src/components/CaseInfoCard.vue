<template>
  <InfoCard card-id="case" title="案件信息">
    <div class="card-item">
      <div class="item-label">今年刑事案件：</div>
      <div class="item-value highlight">{{ caseInfo.thisYear.criminal }}件</div>
    </div>
    <div class="card-item">
      <div class="item-label">今年行政案件：</div>
      <div class="item-value">{{ caseInfo.thisYear.administrative }}件</div>
    </div>
    <div class="card-item">
      <div class="item-label">主要类型：</div>
      <div class="item-value">盗窃{{ caseInfo.thisYear.theft }}件，抢劫{{ caseInfo.thisYear.robbery }}件</div>
    </div>
    <div class="card-item">
      <div class="item-label">去年同期：</div>
      <div class="item-value">刑事{{ caseInfo.lastYear.criminal }}件，行政{{ caseInfo.lastYear.administrative }}件</div>
    </div>
    <div class="card-item">
      <div class="item-label">同比变化：</div>
      <div :class="['item-value', getChangeClass('criminal')]">
        刑事案件{{ getChangeText('criminal') }}
      </div>
    </div>
  </InfoCard>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import InfoCard from './InfoCard.vue'
import { usePanoramaStore } from '@/stores/panorama'

const store = usePanoramaStore()
const caseInfo = computed(() => store.unitData.caseInfo)

const getChangeText = (type: 'criminal' | 'administrative') => {
  const thisYear = caseInfo.value.thisYear[type]
  const lastYear = caseInfo.value.lastYear[type]
  const change = thisYear - lastYear
  
  if (change > 0) {
    return `增加${change}件`
  } else if (change < 0) {
    return `减少${Math.abs(change)}件`
  } else {
    return '持平'
  }
}

const getChangeClass = (type: 'criminal' | 'administrative') => {
  const thisYear = caseInfo.value.thisYear[type]
  const lastYear = caseInfo.value.lastYear[type]
  const change = thisYear - lastYear
  
  if (change > 0) {
    return 'warning'
  } else if (change < 0) {
    return 'success'
  } else {
    return ''
  }
}
</script>

<style scoped>
.card-item {
  margin-bottom: 12px;
  padding-bottom: 12px;
  border-bottom: 1px solid rgba(144, 202, 249, 0.2);
}

.card-item:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.item-label {
  color: #90caf9;
  font-weight: 500;
  margin-bottom: 4px;
}

.item-value {
  color: #e3f2fd;
  font-weight: 300;
}

.highlight {
  color: #4fc3f7;
  font-weight: bold;
}

.warning {
  color: #ff8a65;
  font-weight: bold;
}

.success {
  color: #81c784;
  font-weight: bold;
}
</style>
