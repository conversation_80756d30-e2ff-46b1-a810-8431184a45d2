<script setup lang="ts">
import { onMounted, onUnmounted, ref } from 'vue'
import CenterCard from './components/CenterCard.vue'
import SecurityInfoCard from './components/SecurityInfoCard.vue'
import GuardInfoCard from './components/GuardInfoCard.vue'
import CaseInfoCard from './components/CaseInfoCard.vue'
import IndustryInfoCard from './components/IndustryInfoCard.vue'
import BackgroundCheckCard from './components/BackgroundCheckCard.vue'
import PerformanceCard from './components/PerformanceCard.vue'
import RiskAssessmentCard from './components/RiskAssessmentCard.vue'
import ConnectionLines from './components/ConnectionLines.vue'
import ParticleBackground from './components/ParticleBackground.vue'
import { usePanoramaStore } from './stores/panorama'

const store = usePanoramaStore()

// 拖拽相关
const isDragging = ref(false)
const draggedElement = ref<HTMLElement | null>(null)
const dragOffset = ref({ x: 0, y: 0 })

const handleCardMouseDown = (event: CustomEvent) => {
  const { event: e, cardId } = event.detail
  const target = e.currentTarget as HTMLElement
  if (!target) return

  isDragging.value = true
  draggedElement.value = target
  store.startDrag(cardId)

  const rect = target.getBoundingClientRect()
  dragOffset.value = {
    x: e.clientX - rect.left,
    y: e.clientY - rect.top
  }

  e.preventDefault()
}

const handleMouseMove = (e: MouseEvent) => {
  if (!isDragging.value || !draggedElement.value) return

  const x = ((e.clientX - dragOffset.value.x) / window.innerWidth) * 100
  const y = ((e.clientY - dragOffset.value.y) / window.innerHeight) * 100

  const cardId = store.draggedCard
  if (cardId) {
    store.updateCardPosition(cardId, Math.max(0, Math.min(100, x)), Math.max(0, Math.min(100, y)))
  }
}

const handleMouseUp = () => {
  isDragging.value = false
  draggedElement.value = null
  store.endDrag()
}

const resetLayout = () => {
  store.resetLayout()
}

const saveLayout = () => {
  store.saveLayout()
  // 可以添加一个提示消息
  console.log('布局已保存')
}

onMounted(() => {
  document.addEventListener('mousemove', handleMouseMove)
  document.addEventListener('mouseup', handleMouseUp)
  document.addEventListener('cardMouseDown', handleCardMouseDown as EventListener)

  // 加载保存的布局
  store.loadLayout()
})

onUnmounted(() => {
  document.removeEventListener('mousemove', handleMouseMove)
  document.removeEventListener('mouseup', handleMouseUp)
  document.removeEventListener('cardMouseDown', handleCardMouseDown as EventListener)
})
</script>

<template>
  <div class="panorama-container">
    <!-- 背景粒子效果 -->
    <ParticleBackground />

    <!-- 连接线 -->
    <ConnectionLines />

    <!-- 控制按钮 -->
    <div class="controls">
      <button class="control-btn" @click="resetLayout">重置布局</button>
      <button class="control-btn" @click="saveLayout">保存布局</button>
    </div>

    <!-- 中心卡片 -->
    <CenterCard />

    <!-- 信息卡片 -->
    <SecurityInfoCard />
    <GuardInfoCard />
    <CaseInfoCard />
    <IndustryInfoCard />
    <BackgroundCheckCard />
    <PerformanceCard />
    <RiskAssessmentCard />
  </div>
</template>

<style scoped>
.panorama-container {
  width: 100vw;
  height: 100vh;
  position: relative;
  background: linear-gradient(135deg, #0a192f 0%, #0c213a 100%);
  overflow: hidden;
  font-family: 'Microsoft YaHei', Arial, sans-serif;
}



.controls {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
  display: flex;
  gap: 10px;
}

.control-btn {
  background: rgba(25, 118, 210, 0.3);
  border: 1px solid rgba(33, 150, 243, 0.5);
  color: #64b5f6;
  padding: 10px 15px;
  border-radius: 8px;
  cursor: pointer;
  backdrop-filter: blur(5px);
  transition: all 0.3s ease;
  font-family: inherit;
}

.control-btn:hover {
  background: rgba(33, 150, 243, 0.5);
  transform: translateY(-2px);
}
</style>
