<script setup lang="ts">
import { onMounted, onUnmounted, ref } from 'vue'
import CenterCard from './components/CenterCard.vue'
import SecurityInfoCard from './components/SecurityInfoCard.vue'
import GuardInfoCard from './components/GuardInfoCard.vue'
import CaseInfoCard from './components/CaseInfoCard.vue'
import IndustryInfoCard from './components/IndustryInfoCard.vue'
import BackgroundCheckCard from './components/BackgroundCheckCard.vue'
import PerformanceCard from './components/PerformanceCard.vue'
import RiskAssessmentCard from './components/RiskAssessmentCard.vue'
import ConnectionLines from './components/ConnectionLines.vue'
import ParticleBackground from './components/ParticleBackground.vue'
import { usePanoramaStore } from './stores/panorama'

const store = usePanoramaStore()

// 拖拽相关
const isDragging = ref(false)
const draggedElement = ref<HTMLElement | null>(null)
const dragOffset = ref({ x: 0, y: 0 })
const draggedCardId = ref<string | null>(null)

const handleCardMouseDown = (event: CustomEvent) => {
  const { event: e, cardId } = event.detail
  const target = e.currentTarget as HTMLElement
  if (!target) return

  isDragging.value = true
  draggedElement.value = target
  draggedCardId.value = cardId
  store.startDrag(cardId)

  const rect = target.getBoundingClientRect()
  dragOffset.value = {
    x: e.clientX - rect.left,
    y: e.clientY - rect.top
  }

  // 添加拖拽样式
  target.style.zIndex = '1000'
  target.style.pointerEvents = 'none'
  target.classList.add('dragging')

  e.preventDefault()
}

const handleMouseMove = (e: MouseEvent) => {
  if (!isDragging.value || !draggedElement.value || !draggedCardId.value) return

  // 使用requestAnimationFrame来优化性能
  requestAnimationFrame(() => {
    if (!draggedElement.value || !draggedCardId.value) return

    // 直接更新DOM位置，避免响应式延迟
    const newX = e.clientX - dragOffset.value.x
    const newY = e.clientY - dragOffset.value.y

    // 限制在视窗范围内
    const maxX = window.innerWidth - draggedElement.value.offsetWidth
    const maxY = window.innerHeight - draggedElement.value.offsetHeight

    const clampedX = Math.max(0, Math.min(maxX, newX))
    const clampedY = Math.max(0, Math.min(maxY, newY))

    // 使用transform代替left/top，性能更好
    draggedElement.value.style.transform = `translate(${clampedX}px, ${clampedY}px)`

    // 同时更新store中的百分比位置（用于连线计算）
    const percentX = (clampedX / window.innerWidth) * 100
    const percentY = (clampedY / window.innerHeight) * 100
    store.updateCardPosition(draggedCardId.value, percentX, percentY)
  })
}

const handleMouseUp = () => {
  if (draggedElement.value && draggedCardId.value) {
    // 从transform中提取最终位置
    const transform = draggedElement.value.style.transform
    const match = transform.match(/translate\(([^,]+)px,\s*([^)]+)px\)/)

    if (match) {
      const finalX = parseFloat(match[1])
      const finalY = parseFloat(match[2])

      // 转换为百分比并更新store
      const percentX = (finalX / window.innerWidth) * 100
      const percentY = (finalY / window.innerHeight) * 100
      store.updateCardPosition(draggedCardId.value, percentX, percentY)
    }

    // 恢复样式，让Vue重新接管位置控制
    draggedElement.value.style.zIndex = ''
    draggedElement.value.style.pointerEvents = ''
    draggedElement.value.style.transform = ''

    // 移除拖拽类
    draggedElement.value.classList.remove('dragging')
  }

  isDragging.value = false
  draggedElement.value = null
  draggedCardId.value = null
  store.endDrag()
}

const resetLayout = () => {
  store.resetLayout()
}

const saveLayout = () => {
  store.saveLayout()
  // 可以添加一个提示消息
  console.log('布局已保存')
}

onMounted(() => {
  document.addEventListener('mousemove', handleMouseMove)
  document.addEventListener('mouseup', handleMouseUp)
  document.addEventListener('cardMouseDown', handleCardMouseDown as EventListener)

  // 加载保存的布局
  store.loadLayout()
})

onUnmounted(() => {
  document.removeEventListener('mousemove', handleMouseMove)
  document.removeEventListener('mouseup', handleMouseUp)
  document.removeEventListener('cardMouseDown', handleCardMouseDown as EventListener)
})
</script>

<template>
  <div class="panorama-container">
    <!-- 背景粒子效果 -->
    <ParticleBackground />

    <!-- 连接线 -->
    <ConnectionLines />

    <!-- 控制按钮 -->
    <div class="controls">
      <button class="control-btn" @click="resetLayout">重置布局</button>
      <button class="control-btn" @click="saveLayout">保存布局</button>
    </div>

    <!-- 中心卡片 -->
    <CenterCard />

    <!-- 信息卡片 -->
    <SecurityInfoCard />
    <GuardInfoCard />
    <CaseInfoCard />
    <IndustryInfoCard />
    <BackgroundCheckCard />
    <PerformanceCard />
    <RiskAssessmentCard />
  </div>
</template>

<style scoped>
.panorama-container {
  width: 100vw;
  height: 100vh;
  position: relative;
  background: linear-gradient(135deg, #0a192f 0%, #0c213a 100%);
  overflow: hidden;
  font-family: 'Microsoft YaHei', Arial, sans-serif;
}



.controls {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
  display: flex;
  gap: 10px;
}

.control-btn {
  background: rgba(25, 118, 210, 0.3);
  border: 1px solid rgba(33, 150, 243, 0.5);
  color: #64b5f6;
  padding: 10px 15px;
  border-radius: 8px;
  cursor: pointer;
  backdrop-filter: blur(5px);
  transition: all 0.3s ease;
  font-family: inherit;
}

.control-btn:hover {
  background: rgba(33, 150, 243, 0.5);
  transform: translateY(-2px);
}
</style>
